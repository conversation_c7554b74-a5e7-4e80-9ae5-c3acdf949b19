import os
import json
import argparse
from pathlib import Path
from typing import Any, Dict, Optional
from dataclasses import dataclass

# Optional imports with fallbacks
try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    yaml = None
    YAML_AVAILABLE = False


@dataclass
class ConfigDefaults:
    """Default configuration values"""
    # Database Configuration
    MONGO_URI: str = "mongodb://localhost:27017"
    DB_NAME: str = "aquesa_management"

    # Monitoring Configuration
    WINDOW_SIZE: int = 10  # days
    HOURLY_THRESHOLD: float = 1.2  # 20% above historical max
    DAILY_THRESHOLD: float = 1.2  # 20% above historical max
    MONITOR_INTERVAL: int = 10  # seconds
    RETRAIN_INTERVAL: int = 3600  # seconds (1 hour)

    # Leak Detection Parameters
    FORGOTTEN_TAP_THRESHOLD: float = 0.5  # Liters/min (minimum flow to consider)
    FORGOTTEN_TAP_DURATION: int = 30  # Minutes (continuous flow duration)
    TAP_LEAKAGE_THRESHOLD: float = 0.05
    MODEL_FILE: str = "isolation_forest_model.pkl"

    # Server Configuration
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    API_RELOAD: bool = True
    FRONTEND_PORT: int = 3000
    API_BASE_URL: str = "http://localhost:8000/api"

    # Security Settings
    CORS_ORIGINS: str = "http://localhost:3000,http://127.0.0.1:3000"
    CORS_ALLOW_ALL: bool = True

    # Environment Settings
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    USE_MOCK_DATA: bool = False
    LOG_LEVEL: str = "INFO"
    VERBOSE_LOGGING: bool = False

    # Notification Settings
    EMAIL_NOTIFICATIONS: bool = False
    SMTP_SERVER: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    EMAIL_USERNAME: str = ""
    EMAIL_PASSWORD: str = ""
    NOTIFICATION_EMAILS: str = ""

    # Performance Settings
    BATCH_SIZE: int = 1000
    ENABLE_CACHING: bool = True
    CACHE_TTL: int = 300


class DynamicSettings:
    """Dynamic configuration system that supports multiple sources"""

    def __init__(self, config_sources: Optional[list] = None):
        """
        Initialize dynamic settings with configurable sources.

        Args:
            config_sources: List of configuration sources in priority order.
                          If None, uses default sources: [env, file, defaults]
        """
        self.defaults = ConfigDefaults()
        self.config_sources = config_sources or ['env', 'file', 'defaults']
        self._config_cache = {}
        self._config_files_checked = []

        # Load configuration from all sources
        self._load_configuration()

    def _load_configuration(self):
        """Load configuration from all specified sources in priority order"""
        config = {}

        for source in reversed(self.config_sources):  # Reverse to apply in correct priority
            if source == 'defaults':
                config.update(self._load_from_defaults())
            elif source == 'env':
                config.update(self._load_from_environment())
            elif source == 'file':
                config.update(self._load_from_files())
            elif source == 'args':
                config.update(self._load_from_args())
            elif isinstance(source, dict):
                # Direct dictionary configuration
                config.update(source)

        # Set attributes dynamically
        for key, value in config.items():
            setattr(self, key, value)

    def _load_from_defaults(self) -> Dict[str, Any]:
        """Load configuration from defaults"""
        return {
            field.name: getattr(self.defaults, field.name)
            for field in self.defaults.__dataclass_fields__.values()
        }

    def _load_from_environment(self) -> Dict[str, Any]:
        """Load configuration from environment variables"""
        config = {}

        # Check for .env file and load it if present (but don't require it)
        env_file = Path('.env')
        if env_file.exists():
            try:
                from dotenv import load_dotenv
                load_dotenv(env_file)
            except ImportError:
                # dotenv not available, continue without it
                pass

        # Load from environment variables
        for field_name in self.defaults.__dataclass_fields__:
            env_value = os.getenv(field_name)
            if env_value is not None:
                config[field_name] = self._convert_type(env_value, field_name)

        return config

    def _load_from_files(self) -> Dict[str, Any]:
        """Load configuration from config files"""
        config = {}

        # Check for various config file formats
        config_files = [
            'config.json',
            'config.yaml', 'config.yml',
            'aquesa.json',
            'aquesa.yaml', 'aquesa.yml',
            '.aquesa.json',
            '.aquesa.yaml', '.aquesa.yml'
        ]

        for config_file in config_files:
            file_path = Path(config_file)
            if file_path.exists():
                self._config_files_checked.append(str(file_path))
                try:
                    if file_path.suffix.lower() == '.json':
                        with open(file_path, 'r') as f:
                            file_config = json.load(f)
                    elif file_path.suffix.lower() in ['.yaml', '.yml']:
                        if not YAML_AVAILABLE or yaml is None:
                            print(f"Warning: YAML support not available, skipping {config_file}")
                            continue
                        with open(file_path, 'r') as f:
                            file_config = yaml.safe_load(f)
                    else:
                        continue

                    # Convert keys to uppercase to match environment variable format
                    for key, value in file_config.items():
                        config[key.upper()] = value

                except Exception as e:
                    print(f"Warning: Could not load config file {config_file}: {e}")

        return config

    def _load_from_args(self) -> Dict[str, Any]:
        """Load configuration from command line arguments"""
        config = {}

        # Create argument parser
        parser = argparse.ArgumentParser(add_help=False)  # Don't interfere with main app args

        # Add arguments for key configuration options
        parser.add_argument('--mongo-uri', dest='MONGO_URI')
        parser.add_argument('--db-name', dest='DB_NAME')
        parser.add_argument('--api-host', dest='API_HOST')
        parser.add_argument('--api-port', type=int, dest='API_PORT')
        parser.add_argument('--environment', dest='ENVIRONMENT')
        parser.add_argument('--debug', action='store_true', dest='DEBUG')
        parser.add_argument('--no-debug', action='store_false', dest='DEBUG')

        try:
            args, _ = parser.parse_known_args()
            for key, value in vars(args).items():
                if value is not None:
                    config[key] = value
        except:
            # If argument parsing fails, continue without command line args
            pass

        return config

    def _convert_type(self, value: str, field_name: str) -> Any:
        """Convert string value to appropriate type based on default field type"""
        if not hasattr(self.defaults, field_name):
            return value

        default_value = getattr(self.defaults, field_name)
        target_type = type(default_value)

        if target_type == bool:
            return value.lower() in ('true', '1', 'yes', 'on')
        elif target_type == int:
            try:
                return int(value)
            except ValueError:
                return default_value
        elif target_type == float:
            try:
                return float(value)
            except ValueError:
                return default_value
        else:
            return value

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value with optional default"""
        return getattr(self, key, default)

    def set(self, key: str, value: Any) -> None:
        """Set configuration value at runtime"""
        setattr(self, key, value)

    def update(self, config_dict: Dict[str, Any]) -> None:
        """Update multiple configuration values"""
        for key, value in config_dict.items():
            setattr(self, key, value)

    def to_dict(self) -> Dict[str, Any]:
        """Export current configuration as dictionary"""
        return {
            key: getattr(self, key)
            for key in self.defaults.__dataclass_fields__
            if hasattr(self, key)
        }

    def reload(self) -> None:
        """Reload configuration from all sources"""
        self._load_configuration()

    def get_info(self) -> Dict[str, Any]:
        """Get information about configuration sources and loaded files"""
        return {
            'sources': self.config_sources,
            'config_files_found': self._config_files_checked,
            'current_config': self.to_dict()
        }


# Create global settings instance
# You can customize the sources by passing different config_sources
# Examples:
# settings = DynamicSettings(['env', 'file', 'defaults'])  # Default behavior
# settings = DynamicSettings(['file', 'env', 'defaults'])  # Prioritize files over env
# settings = DynamicSettings(['defaults'])  # Only use defaults
# settings = DynamicSettings([{'MONGO_URI': 'custom://uri'}, 'env', 'defaults'])  # Custom config first

settings = DynamicSettings()