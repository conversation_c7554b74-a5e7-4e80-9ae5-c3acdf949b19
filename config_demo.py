#!/usr/bin/env python3
"""
Demonstration of the dynamic configuration system for AQUESA.
This script shows how to use different configuration sources and priorities.
"""

from app.core.config import DynamicSettings
import json


def demo_default_configuration():
    """Demonstrate default configuration loading"""
    print("=" * 60)
    print("1. DEFAULT CONFIGURATION")
    print("=" * 60)
    
    # Load with default sources: env -> file -> defaults
    settings = DynamicSettings()
    
    print(f"MongoDB URI: {settings.MONGO_URI[:30]}...")
    print(f"Database Name: {settings.DB_NAME}")
    print(f"API Port: {settings.API_PORT}")
    print(f"Environment: {settings.ENVIRONMENT}")
    print(f"Debug Mode: {settings.DEBUG}")
    
    info = settings.get_info()
    print(f"\nConfiguration Sources: {info['sources']}")
    print(f"Config Files Found: {info['config_files_found']}")


def demo_custom_sources():
    """Demonstrate custom configuration source priorities"""
    print("\n" + "=" * 60)
    print("2. CUSTOM SOURCE PRIORITIES")
    print("=" * 60)
    
    # Prioritize config files over environment variables
    settings = DynamicSettings(['file', 'env', 'defaults'])
    
    print("Priority: File -> Environment -> Defaults")
    print(f"MongoDB URI: {settings.MONGO_URI[:30]}...")
    print(f"API Port: {settings.API_PORT}")
    
    # Only use defaults (ignore env and files)
    settings_defaults_only = DynamicSettings(['defaults'])
    print("\nDefaults Only:")
    print(f"MongoDB URI: {settings_defaults_only.MONGO_URI}")
    print(f"API Port: {settings_defaults_only.API_PORT}")


def demo_runtime_configuration():
    """Demonstrate runtime configuration changes"""
    print("\n" + "=" * 60)
    print("3. RUNTIME CONFIGURATION")
    print("=" * 60)
    
    settings = DynamicSettings()
    
    print(f"Original API Port: {settings.API_PORT}")
    
    # Change configuration at runtime
    settings.set('API_PORT', 9000)
    print(f"Updated API Port: {settings.API_PORT}")
    
    # Update multiple values
    settings.update({
        'DEBUG': False,
        'ENVIRONMENT': 'production',
        'LOG_LEVEL': 'WARNING'
    })
    
    print(f"Debug Mode: {settings.DEBUG}")
    print(f"Environment: {settings.ENVIRONMENT}")
    print(f"Log Level: {settings.LOG_LEVEL}")


def demo_direct_config():
    """Demonstrate direct configuration injection"""
    print("\n" + "=" * 60)
    print("4. DIRECT CONFIGURATION INJECTION")
    print("=" * 60)
    
    # Inject custom configuration directly
    custom_config = {
        'MONGO_URI': 'mongodb://custom-server:27017',
        'API_PORT': 8080,
        'DEBUG': False,
        'ENVIRONMENT': 'testing'
    }
    
    settings = DynamicSettings([custom_config, 'env', 'defaults'])
    
    print(f"MongoDB URI: {settings.MONGO_URI}")
    print(f"API Port: {settings.API_PORT}")
    print(f"Environment: {settings.ENVIRONMENT}")
    print(f"Debug Mode: {settings.DEBUG}")


def demo_config_export():
    """Demonstrate configuration export"""
    print("\n" + "=" * 60)
    print("5. CONFIGURATION EXPORT")
    print("=" * 60)
    
    settings = DynamicSettings()
    
    # Export current configuration
    config_dict = settings.to_dict()
    
    print("Current configuration (first 10 items):")
    for i, (key, value) in enumerate(config_dict.items()):
        if i >= 10:
            print("... (truncated)")
            break
        # Hide sensitive info
        if 'uri' in key.lower() or 'password' in key.lower():
            display_value = "***HIDDEN***"
        else:
            display_value = value
        print(f"  {key}: {display_value}")
    
    # Save to file
    with open('current_config_export.json', 'w') as f:
        json.dump(config_dict, f, indent=2, default=str)
    
    print("\nConfiguration exported to 'current_config_export.json'")


def main():
    """Run all configuration demonstrations"""
    print("AQUESA Dynamic Configuration System Demo")
    print("This demo shows various ways to configure the AQUESA system")
    
    try:
        demo_default_configuration()
        demo_custom_sources()
        demo_runtime_configuration()
        demo_direct_config()
        demo_config_export()
        
        print("\n" + "=" * 60)
        print("DEMO COMPLETED SUCCESSFULLY")
        print("=" * 60)
        print("\nKey Benefits of Dynamic Configuration:")
        print("✅ No hard dependency on .env files")
        print("✅ Multiple configuration sources (env, files, direct)")
        print("✅ Configurable source priorities")
        print("✅ Runtime configuration changes")
        print("✅ Easy configuration export/import")
        print("✅ Automatic type conversion")
        print("✅ Fallback to sensible defaults")
        
    except Exception as e:
        print(f"\nError during demo: {e}")
        print("This might be due to missing dependencies or configuration issues.")


if __name__ == "__main__":
    main()
